import argparse
import concurrent.futures
import json
import logging
import logging.config
import os
import re
import sys
import uuid
import threading
import urllib.parse
from datetime import datetime, timedelta, timezone
from functools import reduce
from kubernetes import client, config
from kubernetes.stream import stream

from grafana import GrafanaClient

try:
    config.load_incluster_config()
except config.ConfigException:
    config.load_kube_config()
v1 = client.CoreV1Api()


LABEL_RULE = {
    "unique": """Every unique combination of key-value label pairs represents a new time series, which can dramatically increase the amount of data stored. Do not use labels to store dimensions with high cardinality (many different label values), such as user IDs, email addresses, or other unbounded sets of values.""",
    "label_name": """Do not put the label names in the metric name, as this introduces redundancy and will cause confusion if the respective labels are aggregated away.""",
}

LABEL_EXCLUDE=[
    "__name__",
    "app_kubernetes_io_managed_by",
    "app_kubernetes_io_name",
    "apps_kubernetes_io_pod_index",
    "controller_revision_hash",
    "group",
    "instance",
    "job", 
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "kubernetes_io_hostname",
    "pod_template_hash",
    "team",
    # cron jobs
    "batch_kubernetes_io_controller_uid",
    "controller_uid",
    "batch_kubernetes_io_job_name",
    "job_name",
    "exported_instance",
]
LABEL_WARNING={
    "error": {"msg": LABEL_RULE["unique"]}, 
    "path": {"msg": LABEL_RULE["unique"]}, 
    "log": {"msg": LABEL_RULE["unique"]}, }
NAME_WARNING={
    "_not_": {"msg": "metric name is not clear. Bad example: feature_not_enabled 0"}
}

cache_lock = threading.Lock()
METRICS_CACHE = {}
HPA = json.load(open(f"{os.path.dirname(os.path.realpath(__file__))}/hpa.json"))

def get_logger(prefix="", level=logging.NOTSET):
    logger = logging.getLogger(prefix)
    logger.setLevel(level)
    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'simple': {
                'format': "%(asctime)s | %(levelname)s | %(filename)s:%(lineno)d | %(funcName)s | %(message)s",
                'datefmt': '%Y-%m-%d %H:%M:%S',
            },
        },
        'handlers': {
            'console': {
                'level': level,
                'class': 'logging.StreamHandler',
                'formatter': 'simple',
            },
        },
        'loggers': {
            'root': {
                'handlers': ['console'],
                'level': level,
                'propagate': False,
            },
            'kubernetes': {
                'handlers': ['console'],
                'level': "INFO",
                'propagate': False,
            },
        },
    }
    logging.config.dictConfig(logging_config)
    return logger

def get_prom_pod_name():
    lgr.debug("Getting prometheus pod name")
    res = v1.list_namespaced_pod("monitoring")
    for pod in res.items:
        if "prometheus" in pod.metadata.name:
            lgr.debug(f"Prometheus pod name: {pod.metadata.name}")
            return pod.metadata.name
    raise Exception("Can not get Prometheus pod name. You may need to request pods/exec permissions for the cluster.")
        
def get_pod_from_label(label: str):
    res = v1.list_namespaced_pod("xdr-st")
    for pod in res.items:
        if label == pod.metadata.labels.get("app") or label == pod.metadata.labels.get("statefulset_kubernetes_io_pod_name"):
            return pod.metadata.name
    return f"oops, no pod found for label: {label}"

def get_pod_image(pod_list, label: str):
    for pod in pod_list.items:
        if label == pod.metadata.labels.get("app") or label == pod.metadata.name:
            return [{"container": container.name, "image":container.image} for container in pod.spec.containers]       

def exec_cmd(cmd: str):
    lgr.debug(f"Executing cmd: {cmd}")
    ws_client = stream(v1.connect_get_namespaced_pod_exec,
                  prometheus_pod_name, 'monitoring',
                  command=["/bin/sh", "-c", cmd],
                  stderr=True, stdin=False,
                  stdout=True, tty=False,
                  _preload_content=False)
    ws_client.run_forever(timeout=30)
    resp = ws_client.read_stdout()
    return resp

def get_metric(name: str):
    # get list of metrics by name pattern from prometheus
    res = exec_cmd(f"wget -q -T 10 -O - 'http://localhost:9090/api/v1/query?query={{__name__=~\"{name}\"}}'")
    try:
        return res.json()
    except Exception as ex:
        raise Exception(f"Error getting metric: {str(ex)}, {res=}")

def get_metric_range(name: str):
    # get list of metrics by name pattern from prometheus
    end = datetime.now(tz=timezone.utc)
    start = end - timedelta(hours=12)
    start_str = start.strftime("%Y-%m-%dT%H:%M:%S") + "-00:00"
    end_str = end.strftime("%Y-%m-%dT%H:%M:%S") + "-00:00"
    time_param = f"&start={start_str}&end={end_str}&step=250"
    query = f'{{__name__=~"{name}"}}'
    res = exec_cmd(
        f"wget -q -T 60 -O - 'http://localhost:9090/api/v1/query_range?query={urllib.parse.quote_plus(query)}{time_param}'"
    )
    try:
        return json.loads(res)
    except Exception as ex:
        raise Exception(f"Error getting metric: {str(ex)}, {res=}")

def get_prom_targets(job_name=None):
    # get targets from prometheus to check the source metric (only application labels?)
    job = f"&scrapePool={job_name}" if job_name else ""
    res = exec_cmd(f"wget -q -T 10 -O - 'http://localhost:9090/api/v1/targets?state=active{job}'")
    return json.loads(res)

def get_metrics_from_target(target: str) -> list:
    # get all metrics (plain text)
    lgr.debug(f"Getting all raw metrics from target {target}")
    text = exec_cmd(f"wget -q -T 10 -O - '{target}'")
    return text.strip().split('\n')

def get_metric_from_cache(target: str, metric_name: str):
    lgr.debug(f"Getting raw metrics '{metric_name}' from cache {target}")
    m = metric_name
    # hook for buckets to get the all related metrics
    for r in ["_bucket$", "_sum$", "_count$"]:
        m = re.sub(f"{r}$", "", m)
    text = [line for line in METRICS_CACHE[target] if re.search(metric_name, line)]
    return text

def filter_prom_targets(targets: dict, target_names: list):
    # filter targets from prometheus based on the given target names
    results = []
    for target in targets["data"]["activeTargets"]:
        r = { "scrapeUrl": target["scrapeUrl"],
              "pod_name": target["labels"].get("kubernetes_pod_name",target["labels"].get("instance"))}
        target_name = target["labels"].get("app", target["labels"].get("statefulset_kubernetes_io_pod_name"))
        if target_name:
            if target_name in target_names:
                r["app"] = target_name
                results.append(r)
    return results

def filter_prom_targets_reverse(targets: dict, target_name: str):
    results = []
    for target in targets["data"]["activeTargets"]:
        r = { "scrapeUrl": target["scrapeUrl"],
              "pod_name": target["labels"].get("kubernetes_pod_name",target["labels"].get("instance")) }
        if target["labels"].get("app"):
            if target_name in target["labels"].get("app"):
                r["app"] = target["labels"]["app"]
                results.append(r)
        if target["labels"].get("statefulset_kubernetes_io_pod_name"):
            if target_name in target["labels"].get("statefulset_kubernetes_io_pod_name"):
                r["app"] = target["labels"]["statefulset_kubernetes_io_pod_name"]
                results.append(r)
    return results

def check_label_name(s: str):
    # returns True if the label name contains any of the strings in LABEL_WARNING
    if any([x for x in LABEL_WARNING if x in s.lower()]):
        for lw in LABEL_WARNING:
            if lw in s.lower():
                return LABEL_WARNING[lw]["msg"]
    return None

def check_metric_name(s: str):
    # returns True if the metric name contains any of the strings in NAME_WARNING
    if any([x for x in NAME_WARNING if x in s.lower()]):
        for nw in NAME_WARNING:
            if nw in s.lower():
                return NAME_WARNING[nw]["msg"]
    return None

def detect_string_type(s: str):
    # Detect the content type of the given string
    uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')
    if uuid_pattern.match(s):
        return "String type is UUID. The UUID type is too unique for label value, please change it."
    try:
        uuid.UUID(s)
        return "String type is UUID. The UUID type is too unique for label value, please change it"
    except ValueError:
        pass
    if re.match(r'^[^@]+@[^@]+\.[^@]+$', s):
        return "String type is email. Bad practice because it may have too many different values."
    if re.match(r'^[0-9a-f]{32}$', s):
        return "String type is hash. The 'hash' type is too unique for label value, please change it"
    if " " in s:
        return "There is space symbol in value. Please check the value."
    try:
        if int(s) > 599:  # allow http codes only
            return "String type is number. Bad practice because it may have too many different values."
    except ValueError:
        pass
    return None

def metric_labels(metric: dict):
    metric_name = metric["metric"]["__name__"]
    lgr.debug(f"[{metric_name}] Processing metric: {metric_name}")
    # result = {"name": metric_name, "labels": {}, "warnings": [], "hpa": {}}
    if metric_name not in res:
        res[metric_name] = {"name": metric_name, "labels": {}, "warnings": [], "hpa": {}}
        ## check metric name convention
        name_warn = check_metric_name(metric_name)
        if name_warn: res[metric_name]["warnings"].append(name_warn)
    result = res[metric_name]
    
    # checking labels
    for label, value in metric["metric"].items():
        if label in LABEL_EXCLUDE: continue
        if label not in result["labels"]:
            result["labels"][label] = []
            label_name_warn = check_label_name(label)
            if label_name_warn: result["warnings"].append(f"{label_name_warn} '{label}'")
        if value not in result["labels"][label]:
            result["labels"][label].append(value)
            # check value type
            str_type_warn = detect_string_type(value)
            if str_type_warn: result["warnings"].append(f"{str_type_warn} '{label}={value}'")
    
    # getting hpa config
    app_names = result["labels"].get("app", []) + result["labels"].get("statefulset_kubernetes_io_pod_name", [])
    for app_name in app_names:
        # iterate through all the app names associated with the metric
        if not app_name: continue

        lgr.debug(f"[{metric_name}] working on {app_name=}")
        hpa_name = None
        m = re.match(r"xdr-st-\d+-(:?[\w\d\-]+)", app_name)
        if m: hpa_name = m.group(1)
        # Remove suffix like "-0" from statefulset to find hpa
        m1 = re.match(r"(:?[a-z-]+)-[\d]+", hpa_name if hpa_name is not None else app_name)
        if m1: hpa_name = m1.group(1)
        
        if hpa_name:
            hpa_config = HPA.get(hpa_name)
            if hpa_config:
                lgr.debug(f"[{metric_name}] HPA for {app_name} is {hpa_config}")
                result["hpa"][hpa_name] = hpa_config
            else:
                lgr.error(f"[{metric_name}] Failed to get HPA config for {app_name} ({hpa_name=}). Using default min=1, max=1.")
                result["hpa"][hpa_name] = {"min": 1, "max": 1}
                warn = f"HPA is not defined for '{hpa_name}' ({app_name})"
                if warn not in result["warnings"]:
                    result["warnings"].append(warn) 
        else:
            lgr.error(f"[{metric_name}] There is no hpa_name for {app_name=}. Using default min=1, max=1.")
            result["hpa"][app_name] = {"min": 1, "max": 1}
    # hook for recording rules that does not have regular labels 'app' and 'statefulset_kubernetes_io_pod_name'
    if len(result["hpa"]) == 0:
        result["hpa"]["prometheus"] = {"min": 1, "max": 1}
    # define targets for the metric
    result['targets'] = filter_prom_targets(targets, app_names)

def get_raw_metrics(metric):
     for t in metric['targets']:
        if t["scrapeUrl"] == "fix-me-placeholder": continue
        if not METRICS_CACHE.get(t["pod_name"]):
            with cache_lock:
                METRICS_CACHE[t["pod_name"]] = get_metrics_from_target(t["scrapeUrl"])
        t["raw_metric"] = get_metric_from_cache(t["pod_name"], metric['name'])
        t["image"] = get_pod_image(pod_list, t["app"])

def raw_to_ts(text: str):
    metric_re = re.compile(r'^(?P<metric>[a-zA-Z_:][a-zA-Z0-9_:]*]*)(?P<labels>\{.*\})?[\t ]*(?P<value>[0-9E.]*)[\t ]*(?P<timestamp>[0-9]+)?$')
    labels_re = re.compile(r'(?P<key>[\w\d_]+)=\"(?P<value>[\w\d _\-/.,\\]+)\"')
    lgr.debug(f"raw text {text=}")
    m = metric_re.match(text)
    if m:
        metric_ts = {"metric":{"__name__": m.group('metric')}, "value": [m.group('value')]}
        if m.group('labels'):
            for l in labels_re.findall(m.group('labels')):
                metric_ts["metric"][l[0]] = l[1]
                    
        lgr.debug(f"{metric_ts=}")
        return metric_ts


if __name__ == "__main__":
    # Usage: python cardinality.py 'analytics_.*'
    # Note: the script uses default kubectl config
    parser = argparse.ArgumentParser(description='''The script requests metrics from ST-Prometheus and analyze them (calculate metrics cardinality). 
                                     If metric is not exists in Prometheus, the script will try to search the raw metric on pods monitored by Prometheus.
                                     Example: "python cardinality.py -raw /tmp/metrics.txt -t api 'xdr_.*'"
                                     An easy way to trigger raw metric search is requesting the part of metric without wildcard like "xdr_" which is not exists in Prometheus, but the script will find in on targets''')
    parser.add_argument('metric_regex', metavar='METRIC_NAME', type=str,
                    help='metric name or Prometheus regex. Example: "xdr_retention_enforcement_status", "xdr_retention_.*"')
    parser.add_argument('-t', '--target', metavar='TARGET_NAME', type=str, nargs='?', 
                    help='name of target to get metrics from. Works only if prometheus does not have the metric. Example: "api"')
    parser.add_argument('-j', '--job', metavar='JOB_NAME', type=str, nargs='?', 
                    help='filter targets by Prometheus job name. Example: "kubernetes-pods"')
    parser.add_argument('-w', '--workers', metavar='WORKERS', type=int, default=20, nargs='?', 
                    help='number of workers to scrape raw metrics. Default is: 20')
    parser.add_argument('-g', '--grafana', metavar='GRAFANA', action=argparse.BooleanOptionalAction,
                    help='Use Grafana-dev for metrics request. It requires GRAFANA_API_KEY defined. Script will try to take it from ~/.grafanatoken-dev if not defined')
    parser.add_argument('-skip-verify', '--skip-verification' , metavar='SKIP_VERIFICATION', action=argparse.BooleanOptionalAction,
                    help='Allow trusting any SSL certificate for Grafana requests. Use with caution.')
    parser.add_argument('-lcaas', '--lcaas-id', metavar='LCAAS_ID', type=str, nargs='?',
                    help='Lcaas ID for the tenant. Need to filter results from Grafana. Example: "--lcaas-id 012345"')
    parser.add_argument('-o', '--output-file', metavar='OUTPUT_FILE', type=str, nargs='?', 
                    help='name of file for saving output. stdout used if the flag is not provided')
    parser.add_argument('-f', '--format', metavar='FORMAT', type=str, choices=['json', 'csv', 'table'], default='json',
                    help='Output format. Supported formats: json, csv, table. Default is json')
    parser.add_argument('-raw', '--raw-metrics-file', metavar='RAW_FILE', type=str, nargs='?', # default='raw_metric.txt',
                    help='name of file for saving raw metrics.')
    parser.add_argument('-skip-raw', '--skip-raw-metrics', action=argparse.BooleanOptionalAction,
                    help='ignore raw metrics (script will not fetch raw metric data from targets)')
    parser.add_argument('-l', '--log_level', metavar='LOG_LEVEL', type=str, nargs='?', default='CRITICAL',
                    help='log level for script output. Default is CRITICAL (shows only json with results)')
    args = parser.parse_args()

    lgr = get_logger(level=args.log_level)

    results = []
    bread_crumbs = []
    prom_results = []
    ts_counter = {}

    # grafana code should run first and put data to 'prom_results' to let code see the data is there and we are ready to calculate cardinality
    if args.grafana:
        if not args.lcaas_id:
            lgr.critical(f"--lcass-id argument was not set")
            sys.exit(1)
        gc = GrafanaClient(skip_verify=args.skip_verification)
        prom_results = gc.run_query_range(args.metric_regex, args.lcaas_id)
        targets = {"data":{"activeTargets": []}}

    if len(prom_results) == 0:
        if args.grafana:
            lgr.critical(f"Metric(s) was not found in Grafana. Trying ST-Prometheus with port forwarding")
        prometheus_pod_name = get_prom_pod_name()
        targets = get_prom_targets(job_name=args.job)
        lgr.debug("All Prometheus targets:")
        for target in targets["data"]["activeTargets"]:
            lgr.debug(f"{target=}")
        pod_list = v1.list_pod_for_all_namespaces()
        # getting requested metric from Prometheus
        prometheus_res = get_metric_range(args.metric_regex)
        prom_results = prometheus_res["data"]["result"]
    
    
    if len(prom_results) == 0:
        lgr.warning(f"Prometheus response: {prometheus_res}")
        lgr.warning(f"No results from Prometheus. Checking raw metrics on targets")
        # checking targets and raw metrics
        tgs = targets["data"]["activeTargets"] if not args.target else filter_prom_targets_reverse(targets, args.target)
        if args.target:
            lgr.debug("Filtered Prometheus targets:")
            for target in tgs:
                lgr.debug(f"{target=}")
        prom_results = []

        m = args.metric_regex
        # hook for buckets to get the all related metrics
        for r in ["_bucket$", "_sum$", "_count$"]:
            m = re.sub(f"{r}$", "", m)

        def get_target_ts(target: dict):
            if target.get("labels"):
                pod_name = target["labels"].get("kubernetes_pod_name", target["labels"].get("pod_name"))
            else:
                pod_name = target.get("kubernetes_pod_name", target.get("pod_name"))
                
            if not METRICS_CACHE.get(pod_name):
                with cache_lock:
                    METRICS_CACHE[pod_name] = get_metrics_from_target(target["scrapeUrl"])
            raws = get_metric_from_cache(pod_name, m)
            parsed_metrics = []  # metrics names for exclude
            # raw_counter = 0
            for raw in raws:
                ts = raw_to_ts(raw)
                if ts:
                    lgr.debug(f"{target=}")
                    for l in ["app", "statefulset_kubernetes_io_pod_name"]:
                        if target.get(l):
                            ts["metric"][l] = target.get(l)
                        if target.get("labels"):
                            if target["labels"].get(l):
                                ts["metric"][l] = target["labels"].get(l)
                    prom_results.append(ts)
                    parsed_metrics.append(ts["metric"]["__name__"])
                    # ts_parsed = True
            # excluding lines with the metric name from raws. Only comments without metrics will be in that list
            for metric in parsed_metrics:
                for raw in raws:
                    if metric in raw:
                        raws.remove(raw)
            
            if len(raws) > 0:
                bread_crumbs.append(f"# TARGET: {pod_name}, metric_name={m}")
                for r in raws:
                    bread_crumbs.append(r)
                

        with concurrent.futures.ThreadPoolExecutor(max_workers=args.workers) as executor:
            executor.map(
                get_target_ts,
                tgs
            )
        if len(prom_results) == 0:
            lgr.critical(f"Can not find metric on targets. Please check your metric name(regex): '{args.metric_regex}'")
            lgr.debug(f"Scanning all cached metrics in case to find any comment for: '{args.metric_regex}'")
            for k,v in METRICS_CACHE.items():
                for line in v:
                    if re.search(m, line):
                        lgr.critical(f"Found comment on target {k}: {line}")
            sys.exit(1)
    else:
        lgr.info(f"Got {len(prom_results)} metrics from the Prometheus")

    # need to merge results (because prometheus gives time-series)
    res = {}
    for m in prom_results:
        ts_counter[m['metric']['__name__']] = ts_counter[m['metric']['__name__']]+1 if ts_counter.get(m['metric']['__name__']) else 1
        metric_labels(m)

    # results have merged. can calculate cardinality
    for k,result in res.items():
        lgr.debug(f"Calculating cardinality for {k}")
        pods_min = reduce(lambda x, y: x+y, [v["min"] for v in result["hpa"].values()]) if result.get("hpa") else 1
        pods_max = reduce(lambda x, y: x+y, [v["max"] for v in result["hpa"].values()]) if result.get("hpa") else 1
        cardinality = 1
        # multiply labels to calculate cardinality for one pod
        for c in [len(v) for v in result["labels"].values()]: cardinality = cardinality * c
        cardinality_text = " * ".join([f"{len(v)}({k} labels)" for k,v in result["labels"].items()])
        lgr.debug(f"Cardinality formula: {cardinality_text} = {cardinality}")
        if result.get("hpa"):
            pods_min_text = " + ".join([f"{v['min']}({k} pods)" for k,v in result["hpa"].items()])
            pods_max_text = " + ".join([f"{v['max']}({k} pods)" for k,v in result["hpa"].items()])
            lgr.debug(f"Cardinality formula pods_min: {pods_min_text} = {pods_min}")
            lgr.debug(f"Cardinality formula pods_max: {pods_max_text} = {pods_max}")
        
        result['current_ts_count'] = ts_counter[result['name']]
        result['cardinality_formula'] = {
            "per_pod": cardinality_text,
            "current": f"{cardinality_text} * {len(result['targets'])}(targets)",
            "min": f"{cardinality_text} * ({pods_min_text})",
            "max": f"{cardinality_text} * ({pods_max_text})",
            "avg": f"(({cardinality_text} * ({pods_min_text})) + ({cardinality_text} * ({pods_max_text}))) / 2",
        }
        result['cardinality'] = {
            "per_pod": cardinality,
            "current": cardinality * len(result['targets']),
            "min": cardinality * pods_min,
            "max": cardinality * pods_max,
            "avg": ((cardinality * pods_min) + (cardinality * pods_max)) / 2,
        }
        # Checking cardinality and add warnings if necessary
        if result['cardinality']["per_pod"] > 21:
            result['warnings'].append(f"Cardinality of {result['name']} is high: {result['cardinality']['per_pod']}")
        if result['cardinality']["avg"] > 1000:
            result['warnings'].append(f"Average cardinality of {result['name']} is high: {result['cardinality']['avg']}")
        if len(result['targets']) > 3:
            result['warnings'].append(f"Number of targets for {result['name']} is high: {len(result['targets'])}")

        results.append(result)
    
    if not args.skip_raw_metrics:
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            res = executor.map(
                get_raw_metrics,
                results
            )
        # raw metrics to file:
        if args.raw_metrics_file:
            with open(args.raw_metrics_file, "w") as f:
                for metric in results:
                    for target in metric["targets"]:
                        if target["app"] == "fix-me-placeholder": continue
                        f.write(f'# TARGET: {target["pod_name"]}, metric_name={metric["name"]}, app={target["app"]} ({target["scrapeUrl"]}), metrics:\n')
                        metric_string = "\n".join(target.get("raw_metric", []))
                        f.write(f'{metric_string}\n\n')

    # adding the fake metric metric_is_not_present to show when a metric is not found
    if len(bread_crumbs) > 0:
        results.append({
            "name": "metric_is_not_present",
            "raw": bread_crumbs
        })

    if args.format in ["csv", "table"]:
        labels = []
        splitter = "," if args.format == "csv" else "|"
        border = "" if args.format == "csv" else "|"
        if args.output_file:
            f = open(args.output_file, "w")

        # getting unique label names
        for record in results:
            if record.get("labels"):
                for label in record["labels"].keys():
                    if label not in labels:
                        labels.append(label)
        labels.sort()

        # writing output
        header_split = "," if args.format == "csv" else "||"
        line = border * 2 + header_split.join(
            [
                "name",
                "cardinality_per_pod",
                "cardinality_current",
                "cardinality_min",
                "cardinality_max",
                "cardinality_avg",
            ]
            + labels
        ) + border * 2 
        if args.output_file:
            f.write(line)
        else:
            print(line)

        for record in results:
            if not record.get("cardinality"):
                lgr.warning(
                    f"Skipping record '{record['name']}' as it does not have cardinality information."
                )
                continue
            row = [
                record["name"],
                record["cardinality"]["per_pod"],
                record["cardinality"]["current"],
                record["cardinality"]["min"],
                record["cardinality"]["max"],
                record["cardinality"]["avg"],
            ]
            for label in labels:
                row.append("; ".join(record["labels"].get(label, ["-"])))

            if args.output_file:
                f.write(border + splitter.join(map(str, row)) + border)
            else:
                print(border + splitter.join(map(str, row)) + border)

        exit(0)


    if args.output_file:
        with open(args.output_file, "w") as f:
            f.write(json.dumps(results, indent=2))
    else:
        print(json.dumps(results, indent=2))
    
