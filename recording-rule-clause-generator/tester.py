import requests
import json
import time
import os
from alive_progress import alive_bar
GRAFANA_URL = "https://grafana.xdr.pan.local:3443"
GRAFANA_TOKEN = os.getenv("GRAFANA_TOKEN") # Your Grafana API Token
DATASOURCE_ID = 160  # The numeric ID of your data source

# Your instant query (e.g., for Prometheus)
PROMQL_QUERY = 'cwp_rpc_client_duration_milliseconds_bucket'

# --- Prepare the API Request ---

# The API endpoint for proxying a query to a data source
api_endpoint = f"{GRAFANA_URL}/api/ds/query"

# The headers for authentication
headers = {
    "Authorization": f"Bearer {GRAFANA_TOKEN}",
    "Content-Type": "application/json",
    "Accept": "application/json",
}

# The payload for an instant query
# We use the current time for the 'to' and 'from' fields for an instant query.
# Grafana's API expects time in milliseconds since epoch.
now_in_ms = int(time.time() * 1000)

payload = {
    "queries": [
        {
            "refId": "A",
            "datasourceId": DATASOURCE_ID,
            "expr": PROMQL_QUERY,
            "instant": True,  # This specifies it's an instant query
            "format": "table" # Use 'table' for easy parsing, or 'time_series'
        }
    ],
    # For instant queries, 'from' and 'to' define the single evaluation point
    "from": str(now_in_ms),
    "to": str(now_in_ms),
}

# --- Execute the Request and Get Results ---
def query_grafana(metric_name: str):
    payload = {
    "queries": [
        {
            "refId": "A",
            "datasourceId": DATASOURCE_ID,
            "expr": metric_name,
            "instant": True,  # This specifies it's an instant query
            "format": "table" # Use 'table' for easy parsing, or 'time_series'
        }
    ],
    # For instant queries, 'from' and 'to' define the single evaluation point
    "from": str(now_in_ms),
    "to": str(now_in_ms),
}
    try:
        print(f"Sending query to {api_endpoint}...")
        response = requests.post(api_endpoint, headers=headers, data=json.dumps(payload), verify=False)
        response.raise_for_status()  # This will raise an exception for HTTP errors (4xx or 5xx)
        label_list = []
        results = response.json()
        print("\n✅ Success! Received response:\n")
        for result in results:
            labels = results["results"]["A"]["frames"][0]["schema"]["fields"]
            if labels != []:
                for label in labels[1]["labels"]:
                    if label != "__name__":
                        label_list.append(label)
            return label_list
    except requests.exceptions.HTTPError as err:
        print(f"❌ HTTP Error: {err}")
        print(f"Response Body: {err.response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")


metrics = []


# read text file metrics-test.txt and add each line to metrics list
with open('recording-rule-clause-generator/metrics-test.txt', 'r') as f:
    for line in f:
        metrics.append(line.strip())
if __name__ == "__main__":
    for metric in metrics:
        labels = {"metric_name": metric, "labels": query_grafana(metric)}
        # write to file 
        with open('recording-rule-clause-generator/metrics-labels.json', 'a') as f:
            json.dump(labels, f, indent=2)
            

