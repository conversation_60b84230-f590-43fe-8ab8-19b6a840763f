{
  "metric_name": "cwp_api_latency_seconds_bucket",
  "labels": []
}{
  "metric_name": "cwp_requests_state_latency_seconds_bucket",
  "labels": [
    "app",
    "app_kubernetes_io_name",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "from_status",
    "group",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "le",
    "module",
    "otel_scope_name",
    "outpost_type",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "scan_region",
    "team",
    "tenant_id",
    "tenant_type",
    "to_status",
    "xdr_id"
  ]
}{
  "metric_name": "cwp_http_server_duration_milliseconds_bucket",
  "labels": [
    "app",
    "app_kubernetes_io_name",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "group",
    "http_method",
    "http_route",
    "http_scheme",
    "http_status_code",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "le",
    "net_host_name",
    "net_host_port",
    "net_protocol_name",
    "net_protocol_version",
    "otel_scope_name",
    "otel_scope_version",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "team",
    "tenant_id",
    "tenant_type",
    "xdr_id"
  ]
}{
  "metric_name": "cwp_http_client_duration_milliseconds_bucket",
  "labels": [
    "app",
    "app_kubernetes_io_name",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "group",
    "http_method",
    "http_status_code",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "le",
    "net_peer_name",
    "otel_scope_name",
    "otel_scope_version",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "team",
    "tenant_id",
    "tenant_type",
    "xdr_id"
  ]
}{
  "metric_name": "cwp_rpc_client_responses_per_rpc_bucket",
  "labels": [
    "app",
    "app_kubernetes_io_name",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "group",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "le",
    "otel_scope_name",
    "otel_scope_version",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "rpc_grpc_status_code",
    "rpc_method",
    "rpc_service",
    "rpc_system",
    "team",
    "tenant_id",
    "tenant_type",
    "xdr_id"
  ]
}{
  "metric_name": "cwp_rpc_client_requests_per_rpc_bucket",
  "labels": [
    "app",
    "app_kubernetes_io_name",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "group",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "le",
    "otel_scope_name",
    "otel_scope_version",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "rpc_grpc_status_code",
    "rpc_method",
    "rpc_service",
    "rpc_system",
    "team",
    "tenant_id",
    "tenant_type",
    "xdr_id"
  ]
}{
  "metric_name": "cwp_rpc_client_duration_milliseconds_bucket",
  "labels": [
    "app",
    "app_kubernetes_io_name",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "group",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "le",
    "otel_scope_name",
    "otel_scope_version",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "rpc_grpc_status_code",
    "rpc_method",
    "rpc_service",
    "rpc_system",
    "team",
    "tenant_id",
    "tenant_type",
    "xdr_id"
  ]
}{
  "metric_name": "cwp_api_requests_total",
  "labels": []
}{
  "metric_name": "cwp_api_latency_seconds_sum",
  "labels": []
}{
  "metric_name": "cwp_api_latency_seconds_count",
  "labels": []
}{
  "metric_name": "cwp_api_concurrent_requests",
  "labels": []
}{
  "metric_name": "cwp_asset_filter_scan_requests_total",
  "labels": [
    "allowed",
    "app",
    "app_kubernetes_io_name",
    "asset_type",
    "event_type",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "group",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "scan_type",
    "team",
    "tenant_id",
    "tenant_type",
    "xdr_id"
  ]
}{
  "metric_name": "cwp_rpc_client_response_size_bytes_bucket",
  "labels": [
    "app",
    "app_kubernetes_io_name",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "group",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "le",
    "otel_scope_name",
    "otel_scope_version",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "rpc_method",
    "rpc_service",
    "rpc_system",
    "team",
    "tenant_id",
    "tenant_type",
    "xdr_id"
  ]
}{
  "metric_name": "cwp_instance_handle_duration_seconds_bucket",
  "labels": [
    "app",
    "app_kubernetes_io_name",
    "cloud_provider",
    "exported_instance",
    "exported_job",
    "exported_lcaas_id",
    "fatalErrorCode",
    "group",
    "instance",
    "instance_name",
    "job",
    "kubernetes_namespace",
    "kubernetes_pod_name",
    "lcaas_id",
    "le",
    "otel_scope_name",
    "pod_template_hash",
    "product_tier",
    "product_type",
    "scan_region",
    "status",
    "team",
    "tenant_id",
    "tenant_type",
    "xdr_id"
  ]
}