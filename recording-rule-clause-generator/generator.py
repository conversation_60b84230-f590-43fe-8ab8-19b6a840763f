# this file takes a list of metrics and generates a recording rule clause for each metric by querying grafana.
# our base labels should always be included (product_code, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) and the diff should be added to the base labels 

import requests
import json
import sys
import os
import time
import random
import string
import argparse
from typing import List, Dict, Any

"""

POST /api/ds/query HTTP/1.1
Accept: application/json
Content-Type: application/json

{
   "queries":[
      {
         "refId":"A",
         "scenarioId":"csv_metric_values",
         "datasource":{
            "uid":"PD8C576611E62080A"
         },
         "format": "table",
         "maxDataPoints":1848,
         "intervalMs":200,
         "stringInput":"1,20,90,30,5,0"
      }
   ],
   "from":"now-5m",
   "to":"now"
}

"""


# id 160 is kr-prometheus
GRAFANA_TOKEN = os.getenv("GRAFANA_TOKEN")
endpoint = "https://grafana.xdr.pan.local:3443/api/datasources/proxy/160/api/v1/query?query=cwp_api_latency_seconds_bucket"

metric_name = "cwp_api_latency_seconds_bucket"

queryx = f"{endpoint}{metric_name}"

headers = {"Content-Type": "application/json", "Authorization": f"Bearer {GRAFANA_TOKEN}"}
response = requests.get(endpoint, headers=headers, verify=False)

if response.status_code != 200:
    raise Exception(f"Failed to get metric labels: {response.text}")

print(queryx)
print(response)


result = data["data"]["result"]
labels = set()
for r in result:
    for k in r["metric"].keys():
        labels.add(k)

print(labels)